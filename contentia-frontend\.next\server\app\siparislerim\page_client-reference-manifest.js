globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/siparislerim/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/global-error.tsx":{"*":{"id":"(ssr)/./src/app/global-error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(ssr)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/contentiaio/Contentiaio.tsx":{"*":{"id":"(ssr)/./src/components/contentiaio/Contentiaio.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ordersNavigations/OrdersProfile.tsx":{"*":{"id":"(ssr)/./src/components/ordersNavigations/OrdersProfile.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ordersNavigations/OrdersOrders.tsx":{"*":{"id":"(ssr)/./src/components/ordersNavigations/OrdersOrders.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/paketler/page.tsx":{"*":{"id":"(ssr)/./src/app/paketler/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ordersNavigations/MyBrands.tsx":{"*":{"id":"(ssr)/./src/components/ordersNavigations/MyBrands.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/authentication/AuthPages.tsx":{"*":{"id":"(ssr)/./src/components/authentication/AuthPages.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/siparis-olustur/page.tsx":{"*":{"id":"(ssr)/./src/app/siparis-olustur/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\global-error.tsx":{"id":"(app-pages-browser)/./src/app/global-error.tsx","name":"*","chunks":["app/global-error","static/chunks/app/global-error.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\layout.tsx":{"id":"(app-pages-browser)/./src/app/layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\components\\contentiaio\\Contentiaio.tsx":{"id":"(app-pages-browser)/./src/components/contentiaio/Contentiaio.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\components\\ordersNavigations\\OrdersProfile.tsx":{"id":"(app-pages-browser)/./src/components/ordersNavigations/OrdersProfile.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\components\\ordersNavigations\\OrdersOrders.tsx":{"id":"(app-pages-browser)/./src/components/ordersNavigations/OrdersOrders.tsx","name":"*","chunks":["app/siparislerim/page","static/chunks/app/siparislerim/page.js"],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\paketler\\page.tsx":{"id":"(app-pages-browser)/./src/app/paketler/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\components\\ordersNavigations\\MyBrands.tsx":{"id":"(app-pages-browser)/./src/components/ordersNavigations/MyBrands.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\components\\authentication\\AuthPages.tsx":{"id":"(app-pages-browser)/./src/components/authentication/AuthPages.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\siparis-olustur\\page.tsx":{"id":"(app-pages-browser)/./src/app/siparis-olustur/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Projects\\contentia\\contentia-frontend\\src\\":[],"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\global-error":[],"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\not-found":[],"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\page":[],"D:\\Projects\\contentia\\contentia-frontend\\src\\app\\siparislerim\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/global-error.tsx":{"*":{"id":"(rsc)/./src/app/global-error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(rsc)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(rsc)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/contentiaio/Contentiaio.tsx":{"*":{"id":"(rsc)/./src/components/contentiaio/Contentiaio.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ordersNavigations/OrdersProfile.tsx":{"*":{"id":"(rsc)/./src/components/ordersNavigations/OrdersProfile.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ordersNavigations/OrdersOrders.tsx":{"*":{"id":"(rsc)/./src/components/ordersNavigations/OrdersOrders.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/paketler/page.tsx":{"*":{"id":"(rsc)/./src/app/paketler/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ordersNavigations/MyBrands.tsx":{"*":{"id":"(rsc)/./src/components/ordersNavigations/MyBrands.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/authentication/AuthPages.tsx":{"*":{"id":"(rsc)/./src/components/authentication/AuthPages.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/siparis-olustur/page.tsx":{"*":{"id":"(rsc)/./src/app/siparis-olustur/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}