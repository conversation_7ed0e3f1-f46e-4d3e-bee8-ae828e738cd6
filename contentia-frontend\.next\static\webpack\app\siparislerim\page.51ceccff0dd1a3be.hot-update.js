"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/siparislerim/page",{

/***/ "(app-pages-browser)/./src/components/ordersNavigations/sub-profile/ModelClaim.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/ordersNavigations/sub-profile/ModelClaim.tsx ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelClaim)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/features/profile/orderSlice */ \"(app-pages-browser)/./src/store/features/profile/orderSlice.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ModelClaim(param) {\n    let { orderData } = param;\n    _s();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useDispatch)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { register, handleSubmit, reset, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm)();\n    const onSubmit = async (data)=>{\n        if (!orderData._id) {\n            console.error(\"No order found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const resultAction = await dispatch((0,_store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__.createClaim)({\n                orderId: orderData._id,\n                data: {\n                    claimContent: data.claimContent\n                }\n            }));\n            if (_store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__.createClaim.fulfilled.match(resultAction)) {\n                reset();\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Talep başarıyla oluşturuldu\");\n            } else if (_store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__.createClaim.rejected.match(resultAction)) {\n                throw new Error(resultAction.error.message);\n            }\n        } catch (error) {\n            console.error(\"Failed to submit revision request:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Talep oluşturma başarısız oldu\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 sm:p-5 md:p-6 lg:p-6 rounded-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base font-semibold mb-1\",\n                        children: \"Şikayet Bildirimi\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"L\\xfctfen şikayet bildirmek istediğiniz İ\\xe7erik \\xdcreticisi No ve ilgili i\\xe7erik bağlantı linki ile birlikte, yaşadığınız problemi ve detaylarını belirtin.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-2 sm:mb-3 md:mb-3 lg:mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ...register(\"claimContent\", {\n                                            required: \"Revizyon detayları gereklidir\",\n                                            minLength: {\n                                                value: 10,\n                                                message: \"Lütfen en az 10 karakter girin\"\n                                            }\n                                        }),\n                                        className: \"w-full p-2 sm:p-3 md:p-4 lg:p-4 border rounded-lg focus:outline-none\",\n                                        rows: 6,\n                                        placeholder: \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 29\n                                    }, this),\n                                    errors.claimContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-sm mt-1\",\n                                        children: errors.claimContent.message\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"Button text-white px-8 py-1 rounded-lg font-semibold\",\n                                    children: isSubmitting ? \"Gönderiliyor...\" : \"Gönder\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n                lineNumber: 63,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelClaim.tsx\",\n            lineNumber: 62,\n            columnNumber: 13\n        }, this)\n    }, void 0, false);\n}\n_s(ModelClaim, \"QDCfWo7JrE8c2g1CDVsuYiEsdgw=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_4__.useDispatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm\n    ];\n});\n_c = ModelClaim;\nvar _c;\n$RefreshReg$(_c, \"ModelClaim\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ordersNavigations/sub-profile/ModelClaim.tsx\n"));

/***/ })

});