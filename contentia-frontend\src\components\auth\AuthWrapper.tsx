"use client";

import { useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { toast } from "react-toastify";
import LoadingSpinner from "@/components/loaders/LoadingSpinner";

const AuthWrapper = ({ children }: { children: React.ReactNode }) => {
    const router = useRouter();
    const pathname = usePathname();

    const { user, loading: isLoading } = useSelector(
        (state: RootState) => state.login
    );

    const isCustomerRoute = pathname.startsWith("/siparis-olustur");
    const isAdminRoute = pathname.startsWith("/admin");
    const isAuthPage = ["/giris-yap"].includes(pathname);

    useEffect(() => {
        if (!isLoading) {
            if (isAdminRoute && user?.role !== "admin") {
                router.replace("/");
                toast.error("Yalnızca yönetici erişimi");
            }
            if (isCustomerRoute && user?.role !== "user") {
                router.replace("/giris-yap");
                toast.error("Giriş yapılması gerekiyor");
            }
            if (user && isAuthPage) {
                router.replace("/");
                toast.info("Zaten giriş yaptınız");
            }
        }
    }, [user, isLoading, pathname, router]);

    if (isLoading) {
        return <LoadingSpinner />;
    }

    return <>{children}</>;
};

export default AuthWrapper;
