"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/markalarim/page",{

/***/ "(app-pages-browser)/./src/components/ordersNavigations/MyBrands.tsx":
/*!*******************************************************!*\
  !*** ./src/components/ordersNavigations/MyBrands.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyBrands)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _store_features_profile_brandSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/features/profile/brandSlice */ \"(app-pages-browser)/./src/store/features/profile/brandSlice.ts\");\n/* harmony import */ var _modal_CustomModelAdmin__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../modal/CustomModelAdmin */ \"(app-pages-browser)/./src/components/modal/CustomModelAdmin.tsx\");\n/* harmony import */ var _sub_profile_ModelBrand__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./sub-profile/ModelBrand */ \"(app-pages-browser)/./src/components/ordersNavigations/sub-profile/ModelBrand.tsx\");\n/* harmony import */ var _sub_profile_EditableBrand__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./sub-profile/EditableBrand */ \"(app-pages-browser)/./src/components/ordersNavigations/sub-profile/EditableBrand.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction MyBrands() {\n    _s();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_8__.useDispatch)();\n    const { register, handleSubmit, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingBrandId, setEditingBrandId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const brands = (0,react_redux__WEBPACK_IMPORTED_MODULE_8__.useSelector)({\n        \"MyBrands.useSelector[brands]\": (state)=>state.brand.myBrands\n    }[\"MyBrands.useSelector[brands]\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MyBrands.useEffect\": ()=>{\n            dispatch((0,_store_features_profile_brandSlice__WEBPACK_IMPORTED_MODULE_3__.fetchMyBrands)());\n        }\n    }[\"MyBrands.useEffect\"], [\n        dispatch\n    ]);\n    const openModal = ()=>setIsModalOpen(true);\n    const closeModal = ()=>setIsModalOpen(false);\n    const onSubmit = async (data)=>{\n        if (!editingBrandId) return;\n        const brandData = data.brands[editingBrandId];\n        try {\n            await dispatch((0,_store_features_profile_brandSlice__WEBPACK_IMPORTED_MODULE_3__.updateBrand)({\n                brandId: editingBrandId,\n                data: brandData\n            })).unwrap();\n            react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Marka başarıyla güncellendi!\");\n            setEditingBrandId(null);\n            dispatch((0,_store_features_profile_brandSlice__WEBPACK_IMPORTED_MODULE_3__.fetchMyBrands)());\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to update brand. Please try again.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 sm:px-6 md:px-8 lg:px-28 py-24 sm:py-24 md:py-24 lg:py-24 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 my-4 sm:p-5 sm:my-6 md:p-6 md:my-8 lg:p-6 lg:my-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 mb-4 sm:p-5 sm:mb-5 md:p-6 md:mb-6 lg:p-6 lg:mb-6 bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: openModal,\n                                className: \"flex flex-row items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            width: 16,\n                                            height: 16,\n                                            src: \"/plusIcon.png\",\n                                            alt: \"plus icon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"BlueText text-sm \",\n                                            children: \"Marka Ekle\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit(onSubmit),\n                                children: brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sub_profile_EditableBrand__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        brand: brand,\n                                        register: register,\n                                        isEditing: editingBrandId === brand._id,\n                                        brandId: brand._id,\n                                        setEditingBrandId: setEditingBrandId\n                                    }, brand._id, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                lineNumber: 53,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modal_CustomModelAdmin__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isModalOpen,\n                closeModal: closeModal,\n                title: \"\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sub_profile_ModelBrand__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\MyBrands.tsx\",\n                lineNumber: 88,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MyBrands, \"boVjP1pN8de4g2SL1imhTOJgpM8=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_8__.useDispatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm,\n        react_redux__WEBPACK_IMPORTED_MODULE_8__.useSelector\n    ];\n});\n_c = MyBrands;\nvar _c;\n$RefreshReg$(_c, \"MyBrands\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ordersNavigations/MyBrands.tsx\n"));

/***/ })

});