"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f707dab32240\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXGNvbnRlbnRpYVxcY29udGVudGlhLWZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmNzA3ZGFiMzIyNDBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/AuthWrapper.tsx":
/*!*********************************************!*\
  !*** ./src/components/auth/AuthWrapper.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _components_loaders_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/loaders/LoadingSpinner */ \"(app-pages-browser)/./src/components/loaders/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst AuthWrapper = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, loading: isLoading } = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__.useSelector)({\n        \"AuthWrapper.useSelector\": (state)=>state.login\n    }[\"AuthWrapper.useSelector\"]);\n    const isCustomerRoute = pathname.startsWith(\"/siparis-olustur\");\n    const isAdminRoute = pathname.startsWith(\"/admin\");\n    const isAuthPage = [\n        \"/giris-yap\"\n    ].includes(pathname);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthWrapper.useEffect\": ()=>{\n            if (!isLoading) {\n                if (isAdminRoute && (user === null || user === void 0 ? void 0 : user.role) !== \"admin\") {\n                    router.replace(\"/\");\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Yalnızca yönetici erişimi\");\n                }\n                if (isCustomerRoute && (user === null || user === void 0 ? void 0 : user.role) !== \"user\") {\n                    router.replace(\"/giris-yap\");\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Giriş yapılması gerekiyor\");\n                }\n                if (user && isAuthPage) {\n                    router.replace(\"/\");\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.info(\"You are already logged in\");\n                }\n            }\n        }\n    }[\"AuthWrapper.useEffect\"], [\n        user,\n        isLoading,\n        pathname,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loaders_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\auth\\\\AuthWrapper.tsx\",\n            lineNumber: 40,\n            columnNumber: 16\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_s(AuthWrapper, \"oG6Jy/0ppN3+jWmT4WCGweFZdnE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_5__.useSelector\n    ];\n});\n_c = AuthWrapper;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthWrapper);\nvar _c;\n$RefreshReg$(_c, \"AuthWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/AuthWrapper.tsx\n"));

/***/ })

});