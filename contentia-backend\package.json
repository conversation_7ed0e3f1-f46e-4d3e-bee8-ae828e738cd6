{"name": "contentia-backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "nodemon server.js", "dev": "nodemon server.js", "start:prod": "NODE_ENV=production nodemon server.js", "debug": "ndb server.js", "watch:js": "parcel watch ./public/js/index.js --out-dir ./public/js --out-file bundle.js", "build:js": "parcel watch ./public/js/index.js --out-dir ./public/js --out-file bundle.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@analytics/core": "^0.12.17", "@analytics/google-analytics": "^1.1.0", "@google-analytics/data": "^5.1.0", "@sendgrid/mail": "^8.1.5", "analytics": "^0.8.16", "axios": "^1.7.7", "bcrypt": "^5.1.1", "cloudinary": "^2.5.1", "cloudinary-build-url": "^0.2.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "express-session": "^1.18.1", "google-auth-library": "^9.15.0", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.7.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.15", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "slugify": "^1.6.6", "socket.io": "^4.8.1", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}