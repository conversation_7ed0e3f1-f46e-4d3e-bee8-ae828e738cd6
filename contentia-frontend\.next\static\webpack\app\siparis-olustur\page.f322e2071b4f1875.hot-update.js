"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/siparis-olustur/page",{

/***/ "(app-pages-browser)/./src/components/orders/OrderTabFirst.tsx":
/*!*************************************************!*\
  !*** ./src/components/orders/OrderTabFirst.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TabFirst)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _store_features_admin_addPriceSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/features/admin/addPriceSlice */ \"(app-pages-browser)/./src/store/features/admin/addPriceSlice.ts\");\n/* harmony import */ var _store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/features/profile/orderSlice */ \"(app-pages-browser)/./src/store/features/profile/orderSlice.ts\");\n/* harmony import */ var _store_features_admin_pricingSlice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/features/admin/pricingSlice */ \"(app-pages-browser)/./src/store/features/admin/pricingSlice.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TabFirst(param) {\n    let { setActiveTab } = param;\n    _s();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__.useDispatch)();\n    // State\n    const [selectedQuantity, setSelectedQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedServices, setSelectedServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [activeEdit, setActiveEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTooltipOne, setShowTooltipOne] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeRatio, setActiveRatio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"9:16\");\n    const [activeDuration, setActiveDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"15s\");\n    const [selectedPlatform, setSelectedPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"instagram\");\n    const { data: additionalService } = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__.useSelector)({\n        \"TabFirst.useSelector\": (state)=>state.addPrice\n    }[\"TabFirst.useSelector\"]);\n    const { data: pricing } = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__.useSelector)({\n        \"TabFirst.useSelector\": (state)=>state.pricing\n    }[\"TabFirst.useSelector\"]);\n    const [basePrice, setBasePrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const isCustomMode = selectedCard === \"\";\n    const singleVideo = pricing === null || pricing === void 0 ? void 0 : pricing.find((option)=>option.videoCount === 1);\n    const singleVideoPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TabFirst.useMemo[singleVideoPrice]\": ()=>{\n            return (singleVideo === null || singleVideo === void 0 ? void 0 : singleVideo.finalPrice) || 0;\n        }\n    }[\"TabFirst.useMemo[singleVideoPrice]\"], [\n        singleVideo\n    ]);\n    const getTotalPrice = ()=>{\n        // Base price already includes the quantity multiplication from package selection\n        let total = isCustomMode ? singleVideoPrice * selectedQuantity : basePrice;\n        console.log(\"🚀 ~ getTotalPrice ~ total:\", total);\n        // Add additional services multiplied by number of videos\n        Object.values(selectedServices).forEach((servicePrice)=>{\n            total += servicePrice * selectedQuantity;\n        });\n        return total;\n    };\n    const getSingleVideoPrice = ()=>{\n        const baseVideoPrice = singleVideoPrice;\n        const additionalServicesTotal = Object.values(selectedServices).reduce((acc, price)=>acc + price, 0);\n        return baseVideoPrice + additionalServicesTotal;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TabFirst.useEffect\": ()=>{\n            dispatch((0,_store_features_admin_pricingSlice__WEBPACK_IMPORTED_MODULE_5__.fetchPricePlans)());\n            dispatch((0,_store_features_admin_addPriceSlice__WEBPACK_IMPORTED_MODULE_3__.fetchAdditionalServices)());\n        }\n    }[\"TabFirst.useEffect\"], [\n        dispatch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TabFirst.useEffect\": ()=>{\n            const selectedOption = pricing === null || pricing === void 0 ? void 0 : pricing.find({\n                \"TabFirst.useEffect\": (option)=>option._id === selectedCard\n            }[\"TabFirst.useEffect\"]);\n            if (selectedOption && [\n                3,\n                6,\n                12\n            ].includes(selectedOption.videoCount)) {\n                setSelectedQuantity(selectedOption.videoCount);\n                setBasePrice(selectedOption.finalPrice);\n            }\n        }\n    }[\"TabFirst.useEffect\"], [\n        selectedCard,\n        pricing\n    ]);\n    const handleQuantityChange = (change)=>{\n        setSelectedQuantity((prev)=>{\n            const updated = Math.max(1, prev + change);\n            if ([\n                3,\n                6,\n                12\n            ].includes(updated)) return updated;\n            // Custom mode logic\n            setSelectedCard(\"\");\n            const oneVideo = pricing === null || pricing === void 0 ? void 0 : pricing.find((option)=>option.videoCount === 1);\n            const oneVideoPrice = (oneVideo === null || oneVideo === void 0 ? void 0 : oneVideo.finalPrice) || 0;\n            setBasePrice(oneVideoPrice * updated);\n            return updated;\n        });\n    };\n    const handleCardSelect = (cardId)=>{\n        setSelectedCard(cardId);\n        const selectedOption = pricing === null || pricing === void 0 ? void 0 : pricing.find((option)=>option._id === cardId);\n        if (selectedOption) {\n            if ([\n                3,\n                6,\n                12\n            ].includes(selectedOption.videoCount)) {\n                setSelectedQuantity(selectedOption.videoCount);\n                setBasePrice(selectedOption.finalPrice);\n            } else {\n                setSelectedQuantity(1); // Custom card\n            }\n        }\n    };\n    const handleAddService = (key, price)=>{\n        setSelectedServices((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (updated[key]) {\n                delete updated[key];\n            } else {\n                updated[key] = price;\n            }\n            console.log(\"🚀 ~ handleAddService ~ updated:\", updated);\n            return updated;\n        });\n        if (key === \"edit\") setActiveEdit((prev)=>!prev);\n    };\n    const isServiceSelected = (key)=>selectedServices.hasOwnProperty(key);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        const formData = {\n            noOfUgc: selectedQuantity,\n            basePrice: selectedQuantity === 1 ? singleVideoPrice : basePrice,\n            totalPrice: getTotalPrice(),\n            additionalServices: {\n                platform: selectedPlatform,\n                duration: activeDuration,\n                edit: isServiceSelected(\"edit\"),\n                aspectRatio: activeRatio,\n                share: isServiceSelected(\"share\"),\n                coverPicture: isServiceSelected(\"cover\"),\n                creatorType: isServiceSelected(\"influencer\"),\n                productShipping: isServiceSelected(\"shipping\")\n            }\n        };\n        dispatch((0,_store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_4__.setOrderFormData)(formData));\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Sipariş detayları başarıyla kaydedildi!\");\n        setActiveTab(1);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TabFirst.useEffect\": ()=>{\n            const updated = {\n                ...selectedServices\n            };\n            if (activeDuration === \"30s\") {\n                updated[\"duration\"] = (additionalService === null || additionalService === void 0 ? void 0 : additionalService.thirtySecondDurationPrice) || 0;\n            } else if (activeDuration === \"60s\") {\n                updated[\"duration\"] = (additionalService === null || additionalService === void 0 ? void 0 : additionalService.sixtySecondDurationPrice) || 0;\n            } else {\n                delete updated[\"duration\"];\n            }\n            setSelectedServices(updated);\n        }\n    }[\"TabFirst.useEffect\"], [\n        activeDuration,\n        additionalService\n    ]);\n    const services = [\n        {\n            id: 1,\n            key: \"share\",\n            image: \"/order/orderPic4.svg\",\n            alt: \"Sosyal Medyada Paylaşım\",\n            title: \"Sosyal Medyada Paylaşılsın\",\n            description: \"Hazırlanan içerikler onaylandıktan sonra Contentia.io ve içerik üreticilerinin hesaplarından paylaşılır.\",\n            price: (additionalService === null || additionalService === void 0 ? void 0 : additionalService.sharePrice) || 0\n        },\n        {\n            id: 2,\n            key: \"cover\",\n            image: \"/order/orderPic5.svg\",\n            alt: \"Kapak Görseli\",\n            title: \"Kapak Görseli\",\n            description: \"Hazırlanacak her video için orijinal resim ve kapak görseli hazırlanır.\",\n            price: (additionalService === null || additionalService === void 0 ? void 0 : additionalService.coverPicPrice) || 0\n        },\n        {\n            id: 3,\n            key: \"influencer\",\n            image: \"/order/orderPic6.svg\",\n            alt: \"Influencer Paketi\",\n            title: \"Influencer Paketi\",\n            description: \"Videolarınız Micro Influencerlar tarafından üretilsin.\",\n            price: (additionalService === null || additionalService === void 0 ? void 0 : additionalService.creatorTypePrice) || 0\n        },\n        {\n            id: 4,\n            key: \"shipping\",\n            image: \"/order/orderPic7.svg\",\n            alt: \"Ürün Gönderimi Kargo Ücreti\",\n            title: \"Ürün Gönderimi Kargo Ücreti\",\n            description: \"İçeriklerinizde tanıtımını yapmak istediğiniz ürünü, içerik üreticilerin adreslerine kargolamanız gerekir. Kargo kodu ile gönderimini ek ücret ödemeden sağlayabilirsiniz.\",\n            price: (additionalService === null || additionalService === void 0 ? void 0 : additionalService.shippingPrice) || 0\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \" px-4 sm:px-6 md:px-12 lg:px-24 \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white flex flex-col  justify-center lg:flex-row lg:justify-between p-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:w-1/3 mb-6 md:mb-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/roiImage.jpg\",\n                                    alt: \"Content Creator\",\n                                    className: \"rounded-lg w-full min-h-full h-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:w-2/3 bg-white  lg:px-4 lg:pr-24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-2\",\n                                        children: \"Siparişini \\xd6zelleştir:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"sectionBG py-2 flex flex-row px-2 items-end rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold mb-1 w-1/4\",\n                                                        children: \"Platform:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-1 flex flex-wrap gap-2\",\n                                                        children: [\n                                                            {\n                                                                label: \"Instagram\",\n                                                                value: \"instagram\"\n                                                            },\n                                                            {\n                                                                label: \"TikTok\",\n                                                                value: \"tiktok\"\n                                                            },\n                                                            {\n                                                                label: \"Facebook\",\n                                                                value: \"facebook\"\n                                                            },\n                                                            {\n                                                                label: \"Youtube\",\n                                                                value: \"youtube\"\n                                                            },\n                                                            {\n                                                                label: \"X - Twitter\",\n                                                                value: \"x\"\n                                                            },\n                                                            {\n                                                                label: \"Linkedin\",\n                                                                value: \"linkedin\"\n                                                            }\n                                                        ].map((platform)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"px-3 py-1 min-w-[70px] text-xs rounded-sm border \".concat(selectedPlatform === platform.value ? \"BlueBg text-white\" : \"bg-gray-100\"),\n                                                                onClick: ()=>{\n                                                                    setSelectedPlatform(platform.value);\n                                                                },\n                                                                children: platform.label\n                                                            }, platform.value, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 45\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"sectionBG py-2 flex flex-row px-2 items-end rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold mb-1 w-1/4\",\n                                                        children: \"S\\xfcre:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm px-3 py-1 rounded \".concat(activeDuration === \"15s\" ? \"Button text-white\" : \"bg-white text-black\"),\n                                                                onClick: ()=>setActiveDuration(\"15s\"),\n                                                                children: \"15s\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm px-3 py-1 rounded \".concat(activeDuration === \"30s\" ? \"Button text-white\" : \"bg-white text-black\"),\n                                                                onClick: ()=>setActiveDuration(\"30s\"),\n                                                                children: \"30s\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm px-3 py-1 rounded \".concat(activeDuration === \"60s\" ? \"Button text-white\" : \"bg-white text-black\"),\n                                                                onClick: ()=>setActiveDuration(\"60s\"),\n                                                                children: \"60s\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"sectionBG py-3 px-4 flex items-center justify-between rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold w-1/4\",\n                                                        children: \"Edit:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 w-2/4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm px-4 py-1 rounded-md transition \".concat(activeEdit ? \"BlueBg text-white\" : \"bg-white border border-gray-300 text-black\"),\n                                                                onClick: ()=>{\n                                                                    if (!activeEdit) {\n                                                                        setActiveEdit(true);\n                                                                        setSelectedServices((prev)=>{\n                                                                            var _additionalService_editPrice;\n                                                                            return {\n                                                                                ...prev,\n                                                                                edit: (_additionalService_editPrice = additionalService === null || additionalService === void 0 ? void 0 : additionalService.editPrice) !== null && _additionalService_editPrice !== void 0 ? _additionalService_editPrice : 0\n                                                                            };\n                                                                        });\n                                                                    }\n                                                                },\n                                                                children: \"Evet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm px-4 py-1 rounded-md transition \".concat(!activeEdit ? \"BlueBg text-white\" : \"bg-white border border-gray-300 text-black\"),\n                                                                onClick: ()=>{\n                                                                    if (activeEdit) {\n                                                                        setActiveEdit(false);\n                                                                        setSelectedServices((prev)=>{\n                                                                            const updated = {\n                                                                                ...prev\n                                                                            };\n                                                                            delete updated.edit;\n                                                                            return updated;\n                                                                        });\n                                                                    }\n                                                                },\n                                                                children: \"Hayır\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-1/4 flex justify-end\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-black text-sm p-1 rounded-full\",\n                                                                onMouseEnter: ()=>setShowTooltipOne(true),\n                                                                onMouseLeave: ()=>setShowTooltipOne(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    src: \"/tooltipIcon.png\",\n                                                                    alt: \"tooltip icon\",\n                                                                    height: 16,\n                                                                    width: 16,\n                                                                    className: \"rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            showTooltipOne && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute z-10 top-8 right-0 w-64 bg-gray-800 text-white text-xs p-3 rounded-md shadow-lg\",\n                                                                children: \"İ\\xe7eriklerinizin orijinal versiyonu ile birlikte, se\\xe7tiğiniz sosyal medyaya g\\xf6re paylaşıma hazır versiyonunu alın! Başlıklar, altyazılar, telif hakkı olmayan m\\xfczikler, ge\\xe7işler ve daha fazlasıyla desteklenir.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"sectionBG py-2 flex flex-row px-2 items-end rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold mb-1 w-1/4\",\n                                                        children: \"En Boy Oranı:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm px-3 py-1 rounded \".concat(activeRatio === \"9:16\" ? \"Button text-white\" : \"bg-white text-black\"),\n                                                                onClick: ()=>setActiveRatio(\"9:16\"),\n                                                                children: \"9:16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm px-3 py-1 rounded \".concat(activeRatio === \"16:9\" ? \"Button text-white\" : \"bg-white text-black\"),\n                                                                onClick: ()=>setActiveRatio(\"16:9\"),\n                                                                children: \"16:9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-2 px-4 py-2 sm:my-3 sm:px-8 sm:py-4 md:my-4 md:px-12 md:py-5 lg:my-4 lg:px-16 lg:py-6 bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold pt-4 mb-8\",\n                                children: \"UGC Adedini Se\\xe7:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1  md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: [\n                                    pricing && pricing.filter((option)=>option.videoCount !== 1).map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>handleCardSelect(option._id),\n                                            className: \"p-4 rounded-lg shadow-xl cursor-pointer \".concat(selectedCard === option._id ? \"border-2 BlueBorder sectionBG\" : \"bg-white\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-bold\",\n                                                    children: [\n                                                        option.videoCount,\n                                                        \" Farklı Video\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold mb-2\",\n                                                    children: [\n                                                        option.videoCount,\n                                                        \" Farklı İ\\xe7erik \\xdcretici\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 45\n                                                }, this),\n                                                option.strikeThroughPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"Button inline text-white font-medium rounded-md px-1 py-0.5 text-xs\",\n                                                        children: [\n                                                            (option.strikeThroughPrice - option.finalPrice).toLocaleString(\"tr-TR\"),\n                                                            \" \",\n                                                            \"TL İndirim\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 49\n                                                }, this),\n                                                option.strikeThroughPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold line-through\",\n                                                    children: [\n                                                        option.strikeThroughPrice.toLocaleString(\"tr-TR\"),\n                                                        \" \",\n                                                        \"TL\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-2 text-sm BlueText font-semibold\",\n                                                    children: [\n                                                        option.finalPrice.toLocaleString(\"tr-TR\"),\n                                                        \" \",\n                                                        \"TL\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-black font-thin\",\n                                                            children: [\n                                                                \" \",\n                                                                \"/ \",\n                                                                option.videoCount,\n                                                                \" Video\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, option._id, true, {\n                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 41\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-4 shadow-xl cursor-pointer \".concat(isCustomMode ? \"border-2 BlueBorder sectionBG\" : \"sectionBG\"),\n                                        onClick: ()=>setSelectedCard(\"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-bold mb-2\",\n                                                children: \"İ\\xe7erik Adedi Se\\xe7:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>handleQuantityChange(-1),\n                                                        disabled: selectedQuantity === 1,\n                                                        className: \"border-2 BlueBorder text-white font-medium py-2 w-16 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"BlueText text-3xl font-extrabold\",\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm BlueText font-semibold\",\n                                                        children: [\n                                                            selectedQuantity,\n                                                            \" Video\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>handleQuantityChange(1),\n                                                        className: \"border-2 BlueBorder text-white font-medium py-2 w-16 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"BlueText text-3xl font-extrabold\",\n                                                            children: \"+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-6 text-sm BlueText font-semibold\",\n                                                children: [\n                                                    isCustomMode ? singleVideoPrice.toLocaleString(\"tr-TR\") : \"—\",\n                                                    \" \",\n                                                    \"TL\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-black font-thin\",\n                                                        children: [\n                                                            \" \",\n                                                            \"/ Video\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white px-4 py-2 sm:px-6 sm:py-3 md:px-10 md:py-4 lg:px-16 lg:py-6 rounded-lg \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row justify-between \",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Ek Hizmetlerimiz\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mb-6\",\n                                            children: \"Ek hizmetlerle UGC'lerinizi ve reklam kampanyalarınızı g\\xfc\\xe7lendirin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-2 sm:p-3 md:p-4 lg:p-4 rounded-lg shadow-md flex flex-col lg:flex-row justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                src: service.image,\n                                                alt: service.alt,\n                                                width: 250,\n                                                height: 300,\n                                                className: \"rounded-lg object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2/3 ml-2 sm:ml-3 md:ml-4 lg:ml-4 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-md font-semibold\",\n                                                        children: service.title\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: service.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-black\",\n                                                        children: [\n                                                            service.price.toLocaleString(\"tr-TR\"),\n                                                            \" \",\n                                                            \"TL\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-thin\",\n                                                                children: [\n                                                                    \" \",\n                                                                    \"/ Video\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"mt-2 px-2 py-1 border-2 rounded-md font-semibold w-20 \".concat(selectedServices[service.key] ? \"border-red-500 text-red-500 hover:bg-red-50\" : \"BlueBorder BlueText hover:bg-blue-50\"),\n                                                        onClick: ()=>handleAddService(service.key, service.price || 0),\n                                                        children: selectedServices[service.key] ? \"Kaldır\" : \"Ekle\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, service.id, true, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 left-0 w-full mx-auto lg:px-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-4 flex justify-end items-center border-gray-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mr-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-semibold BlueText\",\n                                            children: [\n                                                \"1 Video x\",\n                                                \" \",\n                                                (isCustomMode ? getSingleVideoPrice() : basePrice / selectedQuantity + Object.values(selectedServices).reduce((acc, price)=>acc + price, 0)).toLocaleString(\"tr-TR\"),\n                                                \" \",\n                                                \"TL\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm BlueText\",\n                                            children: [\n                                                \"Toplam:\",\n                                                \" \",\n                                                ((isCustomMode ? getSingleVideoPrice() : basePrice / selectedQuantity + Object.values(selectedServices).reduce((acc, price)=>acc + price, 0)) * selectedQuantity).toLocaleString(\"tr-TR\"),\n                                                \" \",\n                                                \"TL\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"Button text-white font-semibold py-2 px-4 rounded-lg\",\n                                    children: \"İleri\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n                lineNumber: 215,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\orders\\\\OrderTabFirst.tsx\",\n            lineNumber: 214,\n            columnNumber: 13\n        }, this)\n    }, void 0, false);\n}\n_s(TabFirst, \"DY0J0+EHGe1q7ogD0fVDoky/brs=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_7__.useDispatch,\n        react_redux__WEBPACK_IMPORTED_MODULE_7__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_7__.useSelector\n    ];\n});\n_c = TabFirst;\nvar _c;\n$RefreshReg$(_c, \"TabFirst\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/orders/OrderTabFirst.tsx\n"));

/***/ })

});