<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_10_34)">
<g clip-path="url(#clip1_10_34)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.65297 11.6099C8.85097 11.8289 9.13297 11.9539 9.42897 11.9539C9.72397 11.9539 10.006 11.8289 10.204 11.6099L10.706 11.0499C11.002 10.7209 11.473 10.6149 11.881 10.7839C12.289 10.9539 12.547 11.3619 12.524 11.8039L12.487 12.5509C12.47 12.8469 12.58 13.1359 12.79 13.3459C13 13.5559 13.288 13.6659 13.584 13.6489L14.331 13.6109C14.773 13.5879 15.181 13.8459 15.35 14.2549C15.519 14.6639 15.06 14.4339 14.732 14.7299L14.177 15.2319C13.957 15.4299 14.331 16.2289 14.331 16.5249C14.331 16.8209 14.864 16.9909 15.084 17.1889L15.653 17.9879C15.981 18.2839 15.519 18.7549 15.35 19.1639C15.18 19.5729 14.773 19.8309 14.33 19.8079L13.584 19.7699C13.288 19.7549 12.734 19.7239 12.524 19.9329C12.315 20.1429 12.084 20.5729 12.099 20.8679V21.6159C12.121 22.0569 12.289 22.4659 11.881 22.6349C11.473 22.8039 11.002 23.4739 10.706 23.1449L9.59797 22.6349C9.39997 22.4149 9.41097 22.0249 9.11597 22.0249C8.81997 22.0249 8.85097 21.5899 8.65297 21.8089L7.62097 22.0259C7.32497 22.3539 7.07097 22.4659 6.97597 22.6349C6.88097 22.8019 6.30997 22.0569 6.33297 21.6149L6.36997 20.8679C6.38497 20.5719 6.27397 20.2839 6.06497 20.0749C5.85597 19.8659 5.56897 19.7549 5.27397 19.7699L4.52597 19.8079C4.08497 19.8299 3.67697 19.5719 3.50797 19.1639C3.33897 18.7549 3.44497 18.2839 3.77297 17.9879L4.32897 17.4859C4.54797 17.2869 4.67297 17.0059 4.67297 16.7099C4.67297 16.4139 4.54797 16.1319 4.32897 15.9329L3.77297 15.4309C3.44497 15.1349 3.33897 14.6639 3.50797 14.2549C3.67797 13.8469 4.08497 13.5889 4.52597 13.6109L5.27397 13.6489C5.56897 13.6639 5.85697 13.5539 6.06597 13.3439C6.27597 13.1349 6.38597 12.8469 6.36997 12.5509L6.33297 11.8029C6.30997 11.3619 6.56797 10.9529 6.97597 10.7839C7.38497 10.6149 7.85497 10.7209 8.15097 11.0489L8.65297 11.6099ZM20.758 3.80594C20.964 3.72594 21.198 3.77494 21.354 3.93094C21.511 4.08694 21.56 4.32094 21.479 4.52794L21.108 5.48394C21.019 5.70994 21.088 5.96794 21.278 6.11994L22.078 6.76294C22.25 6.90094 22.324 7.12794 22.267 7.34094C22.21 7.55494 21.327 7.44494 21.107 7.47894L20.8 7.90494C20.56 7.94195 20.37 8.12994 20.334 8.37094L20.177 9.38494C20.143 9.60294 19.984 9.78094 19.77 9.83794C19.557 9.89495 19.33 9.82094 19.191 9.64794L18.311 8.51094C18.16 8.32094 17.902 8.25195 17.675 8.33994L16.719 8.71095C16.513 8.79095 16.516 9.08095 16.359 8.92494C16.203 8.76794 16.154 8.53494 16.234 8.32794L16.606 7.37194C16.694 7.14595 16.626 6.88794 16.436 6.73594L15.636 6.09294C15.465 5.95394 15.39 5.72794 15.448 5.51394C15.505 5.30094 15.682 5.14194 15.9 5.10794L16.914 4.95094C17.154 4.91394 17.343 4.72594 17.38 4.48494L17.537 3.47294C17.57 3.25394 17.73 3.07594 17.943 3.01894C18.157 2.96194 18.383 3.03594 18.522 3.20894L19.165 4.00794C19.317 4.19794 19.575 4.26594 19.801 4.17794L20.758 3.80594Z" fill="#EEEEEE"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.316 8.34477C11.826 8.55677 12.148 9.06577 12.12 9.61777L12.073 10.5518C12.053 10.9218 12.19 11.2818 12.452 11.5438C12.713 11.8058 13.074 11.9438 13.444 11.9228L14.378 11.8758C14.93 11.8468 15.44 12.1688 15.651 12.6798C15.863 13.1898 15.73 13.7798 15.319 14.1488L14.625 14.7758C14.351 15.0238 14.194 15.3758 14.194 15.7458C14.194 16.1158 14.351 16.4668 14.625 16.7148L15.319 17.3418C15.729 17.7118 15.863 18.2998 15.651 18.8108C15.441 19.3208 14.93 19.6438 14.378 19.6148L13.444 19.5678C13.074 19.5478 12.714 19.6878 12.454 19.9478C12.192 20.2098 12.054 20.5698 12.073 20.9388L12.12 21.8728C12.148 22.4248 11.826 22.9338 11.316 23.1458C10.806 23.3568 10.217 23.2248 9.847 22.8148L9.22 22.1148C8.972 21.8408 8.62 21.6848 8.25 21.6848C7.881 21.6848 7.529 21.8408 7.281 22.1148L6.654 22.8148C6.284 23.2248 5.696 23.3568 5.185 23.1448C4.675 22.9348 4.353 22.4248 4.381 21.8728L4.428 20.9388C4.447 20.5698 4.308 20.2098 4.047 19.9488C3.786 19.6878 3.426 19.5488 3.057 19.5678L2.123 19.6148C1.571 19.6428 1.062 19.3208 0.85 18.8108C0.639 18.3008 0.771 17.7118 1.181 17.3418L1.876 16.7148C2.15 16.4668 2.306 16.1148 2.306 15.7448C2.306 15.3758 2.15 15.0238 1.876 14.7758L1.181 14.1488C0.771 13.7788 0.639 13.1908 0.851 12.6798C1.061 12.1698 1.571 11.8478 2.123 11.8758L3.057 11.9228C3.427 11.9428 3.787 11.8028 4.047 11.5428C4.309 11.2808 4.447 10.9208 4.428 10.5518L4.381 9.61777C4.353 9.06577 4.675 8.55677 5.185 8.34477C5.695 8.13377 6.284 8.26577 6.654 8.67577L7.281 9.37577C7.529 9.64977 7.881 9.80577 8.251 9.80577C8.62 9.80577 8.972 9.64977 9.22 9.37577L9.847 8.67577C10.217 8.26577 10.805 8.13277 11.316 8.34477ZM8.25 13.8758C7.214 13.8758 6.375 14.7158 6.375 15.7508C6.375 16.7868 7.215 17.6258 8.25 17.6258C9.286 17.6258 10.125 16.7858 10.125 15.7508C10.125 14.7148 9.285 13.8758 8.25 13.8758ZM18.187 1.04477L19.052 2.11977C19.257 2.37477 19.603 2.46677 19.908 2.34877L21.194 1.84777C21.471 1.73977 21.786 1.80577 21.996 2.01677C22.206 2.22677 22.273 2.54177 22.165 2.81877L21.665 4.10477C21.546 4.40977 21.639 4.75577 21.894 4.96077L22.969 5.82477C23.201 6.01077 23.301 6.31677 23.224 6.60377C23.147 6.89077 22.908 7.10577 22.614 7.15077L21.251 7.36177C20.928 7.41177 20.674 7.66477 20.624 7.98777L20.413 9.35177C20.368 9.64577 20.153 9.88477 19.866 9.96177C19.579 10.0388 19.273 9.93877 19.087 9.70677L18.223 8.63177C18.018 8.37677 17.672 8.28377 17.367 8.40177L16.081 8.90177C15.804 9.00977 15.489 8.94377 15.279 8.73277C15.069 8.52277 15.002 8.20777 15.11 7.93077L15.61 6.64477C15.729 6.33977 15.636 5.99377 15.381 5.78877L14.306 4.92377C14.075 4.73777 13.975 4.43277 14.052 4.14577C14.129 3.85977 14.368 3.64577 14.661 3.59977L16.024 3.38877C16.347 3.33877 16.601 3.08477 16.651 2.76177L16.862 1.39977C16.907 1.10577 17.122 0.866766 17.409 0.789766C17.696 0.712766 18.001 0.812766 18.187 1.04477ZM18.638 4.99977C18.431 4.99977 18.263 5.16777 18.263 5.37477C18.263 5.58177 18.431 5.74977 18.638 5.74977C18.845 5.74977 19.013 5.58177 19.013 5.37477C19.013 5.16777 18.845 4.99977 18.638 4.99977Z" stroke="#484848" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<clipPath id="clip0_10_34">
<rect width="24" height="24" fill="white"/>
</clipPath>
<clipPath id="clip1_10_34">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
