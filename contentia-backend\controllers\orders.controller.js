import Orders from "../models/orders.model.js";
import Claims from "../models/admin/adminClaims.model.js";
import asyncHandler from "../utils/asyncHandler.js";
import ApiResponse from "../utils/ApiResponse.js";
import ApiError from "../utils/ApiError.js";
import { isValidId } from "../utils/commonHelpers.js";
import BrandModel from "../models/brand.model.js";
import { uploadMultipleFilesToCloudinary } from "../utils/Cloudinary.js";
import { sendNotification } from "./admin/adminNotification.controller.js";
import User from "../models/user.model.js";
import { notificationTemplates } from "../helpers/notificationTemplates.js";
import Revision from "../models/revision.model.js";
import Order from "../models/orders.model.js";
import sendEmail from "../utils/email.js";

const createOrder = asyncHandler(async (req, res) => {
    let {
        noOfUgc,
        totalPrice,
        basePrice = 100, // Default base price of 100 TL per video
        orderStatus = "pending",
        paymentStatus = "pending",
        contentsDelivered = 0,
        additionalServices,
        preferences,
        briefContent,
        orderQuota,
        numberOfRequests,
    } = req.body;
    console.log("email from frontend", preferences.email)

    // Calculate total price if not provided
    if (!totalPrice && noOfUgc) {
        totalPrice = basePrice * noOfUgc;
    }

    // Provide default values for additionalServices if not provided or incomplete
    if (!additionalServices) {
        additionalServices = {};
    }

    // Set default values for required fields if they don't exist
    if (!("platform" in additionalServices)) {
        additionalServices.platform = "Instagram";
    }

    if (!("duration" in additionalServices)) {
        additionalServices.duration = "30s";
    }

    if (!("edit" in additionalServices)) {
        additionalServices.edit = true;
    }

    if (!("aspectRatio" in additionalServices)) {
        additionalServices.aspectRatio = "9:16";
    }

    // Set default values for optional fields if they don't exist
    if (!("share" in additionalServices)) {
        additionalServices.share = false;
    }

    if (!("coverPicture" in additionalServices)) {
        additionalServices.coverPicture = false;
    }

    if (!("creatorType" in additionalServices)) {
        additionalServices.creatorType = false;
    }

    if (!("productShipping" in additionalServices)) {
        additionalServices.productShipping = false;
    }

    let fileUrls = [];
    let brand;
    if (briefContent) {
        if (req.files && req.files["uploadFiles"]) {
            const filePaths = req.files["uploadFiles"].map((file) => file.path);

            fileUrls = await uploadMultipleFilesToCloudinary(filePaths);
        }

        if (briefContent.brandName) {
            briefContent.brandName = briefContent?.brandName.trim();
            brand = await BrandModel.findOne({
                brandName: briefContent?.brandName,
            });

            if (!brand) {
                throw new ApiError(400, "Brand not found");
            }
        }
    }

    const allAdminIds = await User.find({ role: "admin" }).select("_id");

    const notificationData = notificationTemplates.orderCreationByCustomer({
        targetUsers: allAdminIds,
        metadata: {
            customerName: req.user.fullName || "John Doe",
            customerEmail: req.user.email || "example.com",
            customerPhoneNumber: req.user.phoneNumber || "123456789",
            status: `The order has been in ${orderStatus} status`,
        },
    });

    await sendNotification(notificationData);

    const newOrder = await Orders.create({
        orderOwner: req.user._id,
        noOfUgc,
        totalPrice,
        basePrice,
        orderStatus,
        paymentStatus,
        contentsDelivered,
        additionalServices,
        preferences,
        briefContent: {
            ...briefContent,
            brandName: brand?.brandName,
            uploadFiles: fileUrls?.map((file) => file.secure_url),
        },
        orderQuota,
        numberOfRequests,
        associatedBrands: brand?._id,
    });

    brand.associatedOrders.push(newOrder._id);
    await brand.save();
    let emailhtml = `<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sipariş Onayı</title>
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; background-color: #f5f5f5;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f5f5f5;">
        <tr>
            <td align="center" style="padding: 40px 20px;">
                <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden;">
                    <!-- Header -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 60px 40px; text-align: center;">
                            <div style="background-color: rgba(255,255,255,0.2); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                                <span style="font-size: 40px;">✓</span>
                            </div>
                            <h1 style="color: #ffffff; font-size: 32px; margin: 0 0 10px 0; font-weight: 700;">Tebrikler!</h1>
                            <p style="color: rgba(255,255,255,0.9); font-size: 18px; margin: 0; font-weight: 400;">Siparişiniz başarıyla oluşturuldu</p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px;">
                            <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                                Siparişinizi aldık ve en kısa sürede hazırlayacağız. Sipariş durumunuzu takip etmek için hesabınıza giriş yapabilirsiniz.
                            </p>
                            
                            <!-- CTA Button -->
                            <table cellpadding="0" cellspacing="0" border="0" width="100%">
                                <tr>
                                    <td align="center" style="padding: 20px 0;">
                                        <a href="#" style="display: inline-block; padding: 16px 32px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);">Siparişimi Takip Et</a>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Divider -->
                            <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
                            
                            <!-- Footer text -->
                            <p style="color: #718096; font-size: 14px; text-align: center; margin: 0;">
                                Sorularınız için bizimle iletişime geçebilirsiniz.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>`
    // this is where you have to send the email
    await sendEmail({
        email: preferences.email,
        subject: "Siparişiniz oluşturuldu!",
        html: emailhtml
    });
    console.log(newOrder)
    return res
        .status(201)
        .json(new ApiResponse(201, newOrder, "Order created successfully"));
});

const getMyOrders = asyncHandler(async (req, res) => {
    const orders = await Orders.find({ orderOwner: req.user._id })
        .populate({
            path: "orderOwner",
            select: "-password",
        })
        .sort({ createdAt: -1 });

    if (!orders) {
        throw new ApiError(404, "No orders found");
    }

    return res
        .status(200)
        .json(new ApiResponse(200, orders, "My Orders retrieved successfully"));
});

const getOrders = asyncHandler(async (req, res) => {
    const creatorId = req.user._id;
    const orders = await Orders.find({
        orderStatus: { $nin: ["completed", "revision"] },
        appliedCreators: { $nin: [creatorId] },
        assignedCreators: { $nin: [creatorId] },
        rejectedCreators: { $nin: [creatorId] },
    })
        .sort({ createdAt: -1 })
        .populate({
            path: "orderOwner",
            select: "-password",
        })
        .populate({
            path: "associatedBrands",
            select: "-associatedOrders",
        });

    return res
        .status(200)
        .json(new ApiResponse(200, orders, "Orders retrieved successfully"));
});

const getOrder = asyncHandler(async (req, res) => {
    const { orderId } = req.params;

    const order = await Orders.findById(orderId);

    if (!order) {
        throw new ApiError(404, "Order not found");
    }

    return res
        .status(200)
        .json(new ApiResponse(200, order, "Order retrieved successfully"));
});

const updateOrder = asyncHandler(async (req, res) => {
    const { orderId } = req.params;
    const {
        noOfUgc,
        orderOwner,
        orderStatus,
        paymentStatus,
        contentsDelivered,
        additionalServices,
        preferences,
        briefContent,
        orderQuota,
        numberOfRequests,
        uploadFiles,
    } = req.body;

    const order = await Orders.findByIdAndUpdate(
        orderId,
        {
            noOfUgc,
            orderOwner,
            orderStatus,
            paymentStatus,
            contentsDelivered,
            additionalServices,
            preferences,
            briefContent,
            orderQuota,
            numberOfRequests,
            uploadFiles,
        },
        { new: true }
    );

    if (!order) {
        throw new ApiError(404, "Order not updated or not found");
    }

    return res
        .status(200)
        .json(new ApiResponse(200, order, "Order updated successfully"));
});

const deleteOrder = asyncHandler(async (req, res) => {
    const { orderId } = req.params;

    isValidId(orderId);
    const order = await Orders.findById(orderId);

    if (!order) {
        throw new ApiError(404, "Order not found");
    }

    if (order.briefContent.brandName) {
        const brand = await BrandModel.findOne({
            brandName: order.briefContent.brandName,
        });

        if (brand) {
            brand.associatedOrders = brand.associatedOrders.filter(
                (order) => order.toString() !== orderId
            );
            await brand.save();
        }
    }

    await Orders.findByIdAndDelete(orderId);

    return res
        .status(200)
        .json(new ApiResponse(200, order, "Order deleted successfully"));
});

const createClaimOnOrder = asyncHandler(async (req, res) => {
    const { orderId } = req.params;
    const { claimContent } = req.body;

    isValidId(orderId);

    if (!claimContent) {
        throw new ApiError(400, "Please provide claim content");
    }

    const order = await Orders.findById(orderId);

    if (!order) {
        throw new ApiError(404, "Order not found");
    }

    const notificationData = notificationTemplates.reportAnOrderFromCustomer({
        orderTitle: order.briefContent.brandName,
        targetUsers: [order.orderOwner],
        metadata: {
            customerName: req.user.fullName,
            customerEmail: req.user.email,
            customerPhoneNumber: req.user.phoneNumber,
            status: `The order has been in ${order.orderStatus} status`,
        },
    });

    await sendNotification(notificationData);

    const claim = await Claims.create({
        customer: order.orderOwner,
        creator: req.user._id,
        order: orderId,
        claimContent,
    });



    return res
        .status(200)
        .json(new ApiResponse(200, claim, "Order retrieved successfully"));
});

const createRevisionOnOrder = asyncHandler(async (req, res) => {
    const { orderId } = req.params;
    const { revisionContent } = req.body;

    isValidId(orderId);

    if (!revisionContent) {
        throw new ApiError(400, "Please provide revision content");
    }

    const order = await Orders.findById(orderId);

    if (!order) {
        throw new ApiError(404, "Order not found");
    }

    const notificationData = notificationTemplates.reportAnOrderFromCustomer({
        orderTitle: order.briefContent.brandName,
        targetUsers: [order.orderOwner],
        metadata: {
            customerName: req.user.fullName,
            customerEmail: req.user.email,
            customerPhoneNumber: req.user.phoneNumber,
            status: `The order has been in ${order.orderStatus} status`,
        },
    });

    await sendNotification(notificationData);

    const revision = await Revision.create({
        customer: req.user._id,
        order: orderId,
        revisionContent,
    });

    order.orderStatus = "revision";
    await order.save();

    return res
        .status(200)
        .json(new ApiResponse(200, revision, "Order retrieved successfully"));
})

const approveRevisionOnOrder = asyncHandler(async (req, res) => {
    const { orderId, revisionId } = req.params;

    isValidId(orderId);
    isValidId(revisionId);

    const [order, revision] = await Promise.all([
        Orders.findById(orderId),
        Revision.findById(revisionId)
    ]);

    if (!order) throw new ApiError(404, "Order not found");
    if (!revision) throw new ApiError(404, "Revision not found");

    order.orderStatus = "active";
    revision.status = "approved";

    await Promise.all([
        order.save(),
        revision.save()
    ]);

    return res
        .status(200)
        .json(new ApiResponse(200, order, "Order updated successfully"));
});

const rejectRevisionOnOrder = asyncHandler(async (req, res) => {
    const { orderId, revisionId } = req.params;

    isValidId(orderId);
    isValidId(revisionId);

    const [order, revision] = await Promise.all([
        Orders.findById(orderId),
        Revision.findById(revisionId)
    ]);

    if (!order) throw new ApiError(404, "Order not found");
    if (!revision) throw new ApiError(404, "Revision not found");

    order.orderStatus = "active";
    revision.status = "rejected";

    await Promise.all([
        order.save(),
        revision.save()
    ]);

    return res
        .status(200)
        .json(new ApiResponse(200, order, "Order updated successfully"));
});

const updatepaymentstatus = asyncHandler(async (req, res) => {
    const { orderid, paymentstatus, } = req.body;

    // Validate input
    if (!orderid || !paymentstatus) {
        res.status(400);
        throw new Error('Order ID and payment status are required');
    }

    // Validate payment status value against the model's enum
    const validPaymentStatuses = ['paid', 'pending', 'refunded', 'cancelled', 'rejected', 'approved'];
    if (!validPaymentStatuses.includes(paymentstatus)) {
        res.status(400);
        throw new Error('Invalid payment status. Must be one of: paid, pending, refunded, cancelled, rejected, approved');
    }

    // Find and update the order, and populate assignedCreators
    let updatedOrder = await Order.findByIdAndUpdate(
        orderid,
        { paymentStatus: paymentstatus },
        {
            new: true, // Return the updated document
            runValidators: true // Run model validators
        }
    ).populate({ path: 'assignedCreators', select: 'email fullName' });

    // Check if order exists
    if (!updatedOrder) {
        res.status(404);
        throw new Error('Order not found');
    }

    // If payment is approved, update order status and send email to creators
    if (paymentstatus === 'approved') {


        // Send email to all assigned creators
        if (Array.isArray(updatedOrder.assignedCreators) && updatedOrder.assignedCreators.length > 0) {
            const creatorEmails = updatedOrder.assignedCreators.map(c => c.email).filter(Boolean);
            if (creatorEmails.length > 0) {
                const emailHtml = `<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yeni Sipariş Atandı</title>
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; background-color: #f5f5f5;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f5f5f5;">
        <tr>
            <td align="center" style="padding: 40px 20px;">
                <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden;">
                    <tr>
                        <td style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 60px 40px; text-align: center;">
                            <div style="background-color: rgba(255,255,255,0.2); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                                <span style="font-size: 40px;">📦</span>
                            </div>
                            <h1 style="color: #ffffff; font-size: 32px; margin: 0 0 10px 0; font-weight: 700;">Yeni Sipariş Atandı!</h1>
                            <p style="color: rgba(255,255,255,0.9); font-size: 18px; margin: 0; font-weight: 400;">Bir sipariş size atandı ve ödeme onaylandı.</p>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 40px;">
                            <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                                Sipariş detaylarını görmek ve çalışmaya başlamak için platforma giriş yapabilirsiniz.
                            </p>
                            <table cellpadding="0" cellspacing="0" border="0" width="100%">
                                <tr>
                                    <td align="center" style="padding: 20px 0;">
                                        <a href="#" style="display: inline-block; padding: 16px 32px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);">Siparişi Görüntüle</a>
                                    </td>
                                </tr>
                            </table>
                            <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
                            <p style="color: #718096; font-size: 14px; text-align: center; margin: 0;">
                                Sorularınız için bizimle iletişime geçebilirsiniz.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>`;
                // Send email to creators
                await Promise.all(
                    creatorEmails.map(email =>
                        sendEmail({
                            email,
                            subject: "Yeni Sipariş Atandı",
                            html: emailHtml
                        })
                    )
                );
            }
        }
    }

    res.status(200).json({ message: 'Payment status updated and notifications sent if applicable' });
});

export {
    createOrder,
    getMyOrders,
    getOrders,
    getOrder,
    updateOrder,
    deleteOrder,
    createClaimOnOrder,
    createRevisionOnOrder,
    approveRevisionOnOrder,
    rejectRevisionOnOrder,
    updatepaymentstatus
};
