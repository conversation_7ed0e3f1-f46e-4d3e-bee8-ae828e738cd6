"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/siparislerim/page",{

/***/ "(app-pages-browser)/./src/components/ordersNavigations/sub-profile/ModelRevision.tsx":
/*!************************************************************************!*\
  !*** ./src/components/ordersNavigations/sub-profile/ModelRevision.tsx ***!
  \************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelRevision)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/features/profile/orderSlice */ \"(app-pages-browser)/./src/store/features/profile/orderSlice.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ModelRevision(param) {\n    let { orderData } = param;\n    _s();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useDispatch)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { register, handleSubmit, reset, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm)();\n    const onSubmit = async (data)=>{\n        if (!orderData._id) {\n            console.error(\"No order found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const resultAction = await dispatch((0,_store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__.createRevision)({\n                orderId: orderData._id,\n                data: {\n                    revisionContent: data.revisionContent\n                }\n            }));\n            if (_store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__.createRevision.fulfilled.match(resultAction)) {\n                reset();\n                await dispatch((0,_store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__.fetchOrders)());\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Revizyon başarıyla oluşturuldu\");\n            } else if (_store_features_profile_orderSlice__WEBPACK_IMPORTED_MODULE_1__.createRevision.rejected.match(resultAction)) {\n                throw new Error(resultAction.error.message);\n            }\n        } catch (error) {\n            console.error(\"Failed to submit revision request:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Revision creation failed\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 sm:p-5 md:p-6 lg:p-6 rounded-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base font-semibold mb-1\",\n                        children: \"Revizyon Talebi\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"L\\xfctfen revizyon talebi oluşturmak istediğiniz İ\\xe7erik \\xdcreticisi No ve İ\\xe7erik bağlantı linki ile, değişiklik istediğiniz detayları belirtin.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-2 sm:mb-3 md:mb-3 lg:mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ...register(\"revisionContent\", {\n                                            required: \"Revizyon detayları gereklidir\",\n                                            minLength: {\n                                                value: 10,\n                                                message: \"Lütfen en az 10 karakter girin\"\n                                            }\n                                        }),\n                                        className: \"w-full p-2 sm:p-3 md:p-4 lg:p-4 border rounded-lg focus:outline-none\",\n                                        rows: 6,\n                                        placeholder: \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 29\n                                    }, this),\n                                    errors.revisionContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-sm mt-1\",\n                                        children: errors.revisionContent.message\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"Button text-white px-8 py-1 rounded-lg font-semibold\",\n                                    children: isSubmitting ? \"Gönderiliyor...\" : \"Gönder\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n                lineNumber: 67,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\contentia\\\\contentia-frontend\\\\src\\\\components\\\\ordersNavigations\\\\sub-profile\\\\ModelRevision.tsx\",\n            lineNumber: 66,\n            columnNumber: 13\n        }, this)\n    }, void 0, false);\n}\n_s(ModelRevision, \"QDCfWo7JrE8c2g1CDVsuYiEsdgw=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_4__.useDispatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm\n    ];\n});\n_c = ModelRevision;\nvar _c;\n$RefreshReg$(_c, \"ModelRevision\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ordersNavigations/sub-profile/ModelRevision.tsx\n"));

/***/ })

});