"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-ga4";
exports.ids = ["vendor-chunks/react-ga4"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-ga4/dist/format.js":
/*!***********************************************!*\
  !*** ./node_modules/react-ga4/dist/format.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = format;\nvar smallWords = /^(a|an|and|as|at|but|by|en|for|if|in|nor|of|on|or|per|the|to|vs?\\.?|via)$/i;\nfunction toTitleCase(string) {\n  return string.toString().trim().replace(/[A-Za-z0-9\\u00C0-\\u00FF]+[^\\s-]*/g, function (match, index, title) {\n    if (index > 0 && index + match.length !== title.length && match.search(smallWords) > -1 && title.charAt(index - 2) !== \":\" && (title.charAt(index + match.length) !== \"-\" || title.charAt(index - 1) === \"-\") && title.charAt(index - 1).search(/[^\\s-]/) < 0) {\n      return match.toLowerCase();\n    }\n    if (match.substr(1).search(/[A-Z]|\\../) > -1) {\n      return match;\n    }\n    return match.charAt(0).toUpperCase() + match.substr(1);\n  });\n}\n\n// See if s could be an email address. We don't want to send personal data like email.\n// https://support.google.com/analytics/answer/2795983?hl=en\nfunction mightBeEmail(s) {\n  // There's no point trying to validate rfc822 fully, just look for ...@...\n  return typeof s === \"string\" && s.indexOf(\"@\") !== -1;\n}\nvar redacted = \"REDACTED (Potential Email Address)\";\nfunction redactEmail(string) {\n  if (mightBeEmail(string)) {\n    console.warn(\"This arg looks like an email address, redacting.\");\n    return redacted;\n  }\n  return string;\n}\nfunction format() {\n  var s = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var titleCase = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  var redactingEmail = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  var _str = s || \"\";\n  if (titleCase) {\n    _str = toTitleCase(s);\n  }\n  if (redactingEmail) {\n    _str = redactEmail(_str);\n  }\n  return _str;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2E0L2Rpc3QvZm9ybWF0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcY29udGVudGlhXFxjb250ZW50aWEtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtZ2E0XFxkaXN0XFxmb3JtYXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzW1wiZGVmYXVsdFwiXSA9IGZvcm1hdDtcbnZhciBzbWFsbFdvcmRzID0gL14oYXxhbnxhbmR8YXN8YXR8YnV0fGJ5fGVufGZvcnxpZnxpbnxub3J8b2Z8b258b3J8cGVyfHRoZXx0b3x2cz9cXC4/fHZpYSkkL2k7XG5mdW5jdGlvbiB0b1RpdGxlQ2FzZShzdHJpbmcpIHtcbiAgcmV0dXJuIHN0cmluZy50b1N0cmluZygpLnRyaW0oKS5yZXBsYWNlKC9bQS1aYS16MC05XFx1MDBDMC1cXHUwMEZGXStbXlxccy1dKi9nLCBmdW5jdGlvbiAobWF0Y2gsIGluZGV4LCB0aXRsZSkge1xuICAgIGlmIChpbmRleCA+IDAgJiYgaW5kZXggKyBtYXRjaC5sZW5ndGggIT09IHRpdGxlLmxlbmd0aCAmJiBtYXRjaC5zZWFyY2goc21hbGxXb3JkcykgPiAtMSAmJiB0aXRsZS5jaGFyQXQoaW5kZXggLSAyKSAhPT0gXCI6XCIgJiYgKHRpdGxlLmNoYXJBdChpbmRleCArIG1hdGNoLmxlbmd0aCkgIT09IFwiLVwiIHx8IHRpdGxlLmNoYXJBdChpbmRleCAtIDEpID09PSBcIi1cIikgJiYgdGl0bGUuY2hhckF0KGluZGV4IC0gMSkuc2VhcmNoKC9bXlxccy1dLykgPCAwKSB7XG4gICAgICByZXR1cm4gbWF0Y2gudG9Mb3dlckNhc2UoKTtcbiAgICB9XG4gICAgaWYgKG1hdGNoLnN1YnN0cigxKS5zZWFyY2goL1tBLVpdfFxcLi4vKSA+IC0xKSB7XG4gICAgICByZXR1cm4gbWF0Y2g7XG4gICAgfVxuICAgIHJldHVybiBtYXRjaC5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIG1hdGNoLnN1YnN0cigxKTtcbiAgfSk7XG59XG5cbi8vIFNlZSBpZiBzIGNvdWxkIGJlIGFuIGVtYWlsIGFkZHJlc3MuIFdlIGRvbid0IHdhbnQgdG8gc2VuZCBwZXJzb25hbCBkYXRhIGxpa2UgZW1haWwuXG4vLyBodHRwczovL3N1cHBvcnQuZ29vZ2xlLmNvbS9hbmFseXRpY3MvYW5zd2VyLzI3OTU5ODM/aGw9ZW5cbmZ1bmN0aW9uIG1pZ2h0QmVFbWFpbChzKSB7XG4gIC8vIFRoZXJlJ3Mgbm8gcG9pbnQgdHJ5aW5nIHRvIHZhbGlkYXRlIHJmYzgyMiBmdWxseSwganVzdCBsb29rIGZvciAuLi5ALi4uXG4gIHJldHVybiB0eXBlb2YgcyA9PT0gXCJzdHJpbmdcIiAmJiBzLmluZGV4T2YoXCJAXCIpICE9PSAtMTtcbn1cbnZhciByZWRhY3RlZCA9IFwiUkVEQUNURUQgKFBvdGVudGlhbCBFbWFpbCBBZGRyZXNzKVwiO1xuZnVuY3Rpb24gcmVkYWN0RW1haWwoc3RyaW5nKSB7XG4gIGlmIChtaWdodEJlRW1haWwoc3RyaW5nKSkge1xuICAgIGNvbnNvbGUud2FybihcIlRoaXMgYXJnIGxvb2tzIGxpa2UgYW4gZW1haWwgYWRkcmVzcywgcmVkYWN0aW5nLlwiKTtcbiAgICByZXR1cm4gcmVkYWN0ZWQ7XG4gIH1cbiAgcmV0dXJuIHN0cmluZztcbn1cbmZ1bmN0aW9uIGZvcm1hdCgpIHtcbiAgdmFyIHMgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IFwiXCI7XG4gIHZhciB0aXRsZUNhc2UgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IHRydWU7XG4gIHZhciByZWRhY3RpbmdFbWFpbCA9IGFyZ3VtZW50cy5sZW5ndGggPiAyICYmIGFyZ3VtZW50c1syXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzJdIDogdHJ1ZTtcbiAgdmFyIF9zdHIgPSBzIHx8IFwiXCI7XG4gIGlmICh0aXRsZUNhc2UpIHtcbiAgICBfc3RyID0gdG9UaXRsZUNhc2Uocyk7XG4gIH1cbiAgaWYgKHJlZGFjdGluZ0VtYWlsKSB7XG4gICAgX3N0ciA9IHJlZGFjdEVtYWlsKF9zdHIpO1xuICB9XG4gIHJldHVybiBfc3RyO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-ga4/dist/format.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-ga4/dist/ga4.js":
/*!********************************************!*\
  !*** ./node_modules/react-ga4/dist/ga4.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = exports.GA4 = void 0;\nvar _gtag = _interopRequireDefault(__webpack_require__(/*! ./gtag */ \"(ssr)/./node_modules/react-ga4/dist/gtag.js\"));\nvar _format = _interopRequireDefault(__webpack_require__(/*! ./format */ \"(ssr)/./node_modules/react-ga4/dist/format.js\"));\nvar _excluded = [\"eventCategory\", \"eventAction\", \"eventLabel\", \"eventValue\", \"hitType\"],\n  _excluded2 = [\"title\", \"location\"],\n  _excluded3 = [\"page\", \"hitType\"];\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/*\nLinks\nhttps://developers.google.com/gtagjs/reference/api\nhttps://developers.google.com/tag-platform/gtagjs/reference\n*/\n/**\n * @typedef GaOptions\n * @type {Object}\n * @property {boolean} [cookieUpdate=true]\n * @property {number} [cookieExpires=63072000] Default two years\n * @property {string} [cookieDomain=\"auto\"]\n * @property {string} [cookieFlags]\n * @property {string} [userId]\n * @property {string} [clientId]\n * @property {boolean} [anonymizeIp]\n * @property {string} [contentGroup1]\n * @property {string} [contentGroup2]\n * @property {string} [contentGroup3]\n * @property {string} [contentGroup4]\n * @property {string} [contentGroup5]\n * @property {boolean} [allowAdFeatures=true]\n * @property {boolean} [allowAdPersonalizationSignals]\n * @property {boolean} [nonInteraction]\n * @property {string} [page]\n */\n/**\n * @typedef UaEventOptions\n * @type {Object}\n * @property {string} action\n * @property {string} category\n * @property {string} [label]\n * @property {number} [value]\n * @property {boolean} [nonInteraction]\n * @property {('beacon'|'xhr'|'image')} [transport]\n */\n/**\n * @typedef InitOptions\n * @type {Object}\n * @property {string} trackingId\n * @property {GaOptions|any} [gaOptions]\n * @property {Object} [gtagOptions] New parameter\n */\nvar GA4 = /*#__PURE__*/function () {\n  function GA4() {\n    var _this = this;\n    _classCallCheck(this, GA4);\n    _defineProperty(this, \"reset\", function () {\n      _this.isInitialized = false;\n      _this._testMode = false;\n      _this._currentMeasurementId;\n      _this._hasLoadedGA = false;\n      _this._isQueuing = false;\n      _this._queueGtag = [];\n    });\n    _defineProperty(this, \"_gtag\", function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      if (!_this._testMode) {\n        if (_this._isQueuing) {\n          _this._queueGtag.push(args);\n        } else {\n          _gtag[\"default\"].apply(void 0, args);\n        }\n      } else {\n        _this._queueGtag.push(args);\n      }\n    });\n    _defineProperty(this, \"_loadGA\", function (GA_MEASUREMENT_ID, nonce) {\n      var gtagUrl = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"https://www.googletagmanager.com/gtag/js\";\n      if (typeof window === \"undefined\" || typeof document === \"undefined\") {\n        return;\n      }\n      if (!_this._hasLoadedGA) {\n        // Global Site Tag (gtag.js) - Google Analytics\n        var script = document.createElement(\"script\");\n        script.async = true;\n        script.src = \"\".concat(gtagUrl, \"?id=\").concat(GA_MEASUREMENT_ID);\n        if (nonce) {\n          script.setAttribute(\"nonce\", nonce);\n        }\n        document.body.appendChild(script);\n        window.dataLayer = window.dataLayer || [];\n        window.gtag = function gtag() {\n          window.dataLayer.push(arguments);\n        };\n        _this._hasLoadedGA = true;\n      }\n    });\n    _defineProperty(this, \"_toGtagOptions\", function (gaOptions) {\n      if (!gaOptions) {\n        return;\n      }\n      var mapFields = {\n        // Old https://developers.google.com/analytics/devguides/collection/analyticsjs/field-reference#cookieUpdate\n        // New https://developers.google.com/analytics/devguides/collection/gtagjs/cookies-user-id#cookie_update\n        cookieUpdate: \"cookie_update\",\n        cookieExpires: \"cookie_expires\",\n        cookieDomain: \"cookie_domain\",\n        cookieFlags: \"cookie_flags\",\n        // must be in set method?\n        userId: \"user_id\",\n        clientId: \"client_id\",\n        anonymizeIp: \"anonymize_ip\",\n        // https://support.google.com/analytics/answer/2853546?hl=en#zippy=%2Cin-this-article\n        contentGroup1: \"content_group1\",\n        contentGroup2: \"content_group2\",\n        contentGroup3: \"content_group3\",\n        contentGroup4: \"content_group4\",\n        contentGroup5: \"content_group5\",\n        // https://support.google.com/analytics/answer/9050852?hl=en\n        allowAdFeatures: \"allow_google_signals\",\n        allowAdPersonalizationSignals: \"allow_ad_personalization_signals\",\n        nonInteraction: \"non_interaction\",\n        page: \"page_path\",\n        hitCallback: \"event_callback\"\n      };\n      var gtagOptions = Object.entries(gaOptions).reduce(function (prev, _ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          key = _ref2[0],\n          value = _ref2[1];\n        if (mapFields[key]) {\n          prev[mapFields[key]] = value;\n        } else {\n          prev[key] = value;\n        }\n        return prev;\n      }, {});\n      return gtagOptions;\n    });\n    _defineProperty(this, \"initialize\", function (GA_MEASUREMENT_ID) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (!GA_MEASUREMENT_ID) {\n        throw new Error(\"Require GA_MEASUREMENT_ID\");\n      }\n      var initConfigs = typeof GA_MEASUREMENT_ID === \"string\" ? [{\n        trackingId: GA_MEASUREMENT_ID\n      }] : GA_MEASUREMENT_ID;\n      _this._currentMeasurementId = initConfigs[0].trackingId;\n      var gaOptions = options.gaOptions,\n        gtagOptions = options.gtagOptions,\n        nonce = options.nonce,\n        _options$testMode = options.testMode,\n        testMode = _options$testMode === void 0 ? false : _options$testMode,\n        gtagUrl = options.gtagUrl;\n      _this._testMode = testMode;\n      if (!testMode) {\n        _this._loadGA(_this._currentMeasurementId, nonce, gtagUrl);\n      }\n      if (!_this.isInitialized) {\n        _this._gtag(\"js\", new Date());\n        initConfigs.forEach(function (config) {\n          var mergedGtagOptions = _objectSpread(_objectSpread(_objectSpread({}, _this._toGtagOptions(_objectSpread(_objectSpread({}, gaOptions), config.gaOptions))), gtagOptions), config.gtagOptions);\n          if (Object.keys(mergedGtagOptions).length) {\n            _this._gtag(\"config\", config.trackingId, mergedGtagOptions);\n          } else {\n            _this._gtag(\"config\", config.trackingId);\n          }\n        });\n      }\n      _this.isInitialized = true;\n      if (!testMode) {\n        var queues = _toConsumableArray(_this._queueGtag);\n        _this._queueGtag = [];\n        _this._isQueuing = false;\n        while (queues.length) {\n          var queue = queues.shift();\n          _this._gtag.apply(_this, _toConsumableArray(queue));\n          if (queue[0] === \"get\") {\n            _this._isQueuing = true;\n          }\n        }\n      }\n    });\n    _defineProperty(this, \"set\", function (fieldsObject) {\n      if (!fieldsObject) {\n        console.warn(\"`fieldsObject` is required in .set()\");\n        return;\n      }\n      if (_typeof(fieldsObject) !== \"object\") {\n        console.warn(\"Expected `fieldsObject` arg to be an Object\");\n        return;\n      }\n      if (Object.keys(fieldsObject).length === 0) {\n        console.warn(\"empty `fieldsObject` given to .set()\");\n      }\n      _this._gaCommand(\"set\", fieldsObject);\n    });\n    _defineProperty(this, \"_gaCommandSendEvent\", function (eventCategory, eventAction, eventLabel, eventValue, fieldsObject) {\n      _this._gtag(\"event\", eventAction, _objectSpread(_objectSpread({\n        event_category: eventCategory,\n        event_label: eventLabel,\n        value: eventValue\n      }, fieldsObject && {\n        non_interaction: fieldsObject.nonInteraction\n      }), _this._toGtagOptions(fieldsObject)));\n    });\n    _defineProperty(this, \"_gaCommandSendEventParameters\", function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommandSendEvent.apply(_this, _toConsumableArray(args.slice(1)));\n      } else {\n        var _args$ = args[0],\n          eventCategory = _args$.eventCategory,\n          eventAction = _args$.eventAction,\n          eventLabel = _args$.eventLabel,\n          eventValue = _args$.eventValue,\n          hitType = _args$.hitType,\n          rest = _objectWithoutProperties(_args$, _excluded);\n        _this._gaCommandSendEvent(eventCategory, eventAction, eventLabel, eventValue, rest);\n      }\n    });\n    _defineProperty(this, \"_gaCommandSendTiming\", function (timingCategory, timingVar, timingValue, timingLabel) {\n      _this._gtag(\"event\", \"timing_complete\", {\n        name: timingVar,\n        value: timingValue,\n        event_category: timingCategory,\n        event_label: timingLabel\n      });\n    });\n    _defineProperty(this, \"_gaCommandSendPageview\", function (page, fieldsObject) {\n      if (fieldsObject && Object.keys(fieldsObject).length) {\n        var _this$_toGtagOptions = _this._toGtagOptions(fieldsObject),\n          title = _this$_toGtagOptions.title,\n          location = _this$_toGtagOptions.location,\n          rest = _objectWithoutProperties(_this$_toGtagOptions, _excluded2);\n        _this._gtag(\"event\", \"page_view\", _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, page && {\n          page_path: page\n        }), title && {\n          page_title: title\n        }), location && {\n          page_location: location\n        }), rest));\n      } else if (page) {\n        _this._gtag(\"event\", \"page_view\", {\n          page_path: page\n        });\n      } else {\n        _this._gtag(\"event\", \"page_view\");\n      }\n    });\n    _defineProperty(this, \"_gaCommandSendPageviewParameters\", function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommandSendPageview.apply(_this, _toConsumableArray(args.slice(1)));\n      } else {\n        var _args$2 = args[0],\n          page = _args$2.page,\n          hitType = _args$2.hitType,\n          rest = _objectWithoutProperties(_args$2, _excluded3);\n        _this._gaCommandSendPageview(page, rest);\n      }\n    });\n    _defineProperty(this, \"_gaCommandSend\", function () {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      var hitType = typeof args[0] === \"string\" ? args[0] : args[0].hitType;\n      switch (hitType) {\n        case \"event\":\n          _this._gaCommandSendEventParameters.apply(_this, args);\n          break;\n        case \"pageview\":\n          _this._gaCommandSendPageviewParameters.apply(_this, args);\n          break;\n        case \"timing\":\n          _this._gaCommandSendTiming.apply(_this, _toConsumableArray(args.slice(1)));\n          break;\n        case \"screenview\":\n        case \"transaction\":\n        case \"item\":\n        case \"social\":\n        case \"exception\":\n          console.warn(\"Unsupported send command: \".concat(hitType));\n          break;\n        default:\n          console.warn(\"Send command doesn't exist: \".concat(hitType));\n      }\n    });\n    _defineProperty(this, \"_gaCommandSet\", function () {\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n      if (typeof args[0] === \"string\") {\n        args[0] = _defineProperty({}, args[0], args[1]);\n      }\n      _this._gtag(\"set\", _this._toGtagOptions(args[0]));\n    });\n    _defineProperty(this, \"_gaCommand\", function (command) {\n      for (var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {\n        args[_key6 - 1] = arguments[_key6];\n      }\n      switch (command) {\n        case \"send\":\n          _this._gaCommandSend.apply(_this, args);\n          break;\n        case \"set\":\n          _this._gaCommandSet.apply(_this, args);\n          break;\n        default:\n          console.warn(\"Command doesn't exist: \".concat(command));\n      }\n    });\n    _defineProperty(this, \"ga\", function () {\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        args[_key7] = arguments[_key7];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommand.apply(_this, args);\n      } else {\n        var readyCallback = args[0];\n        _this._gtag(\"get\", _this._currentMeasurementId, \"client_id\", function (clientId) {\n          _this._isQueuing = false;\n          var queues = _this._queueGtag;\n          readyCallback({\n            get: function get(property) {\n              return property === \"clientId\" ? clientId : property === \"trackingId\" ? _this._currentMeasurementId : property === \"apiVersion\" ? \"1\" : undefined;\n            }\n          });\n          while (queues.length) {\n            var queue = queues.shift();\n            _this._gtag.apply(_this, _toConsumableArray(queue));\n          }\n        });\n        _this._isQueuing = true;\n      }\n      return _this.ga;\n    });\n    _defineProperty(this, \"event\", function (optionsOrName, params) {\n      if (typeof optionsOrName === \"string\") {\n        _this._gtag(\"event\", optionsOrName, _this._toGtagOptions(params));\n      } else {\n        var action = optionsOrName.action,\n          category = optionsOrName.category,\n          label = optionsOrName.label,\n          value = optionsOrName.value,\n          nonInteraction = optionsOrName.nonInteraction,\n          transport = optionsOrName.transport;\n        if (!category || !action) {\n          console.warn(\"args.category AND args.action are required in event()\");\n          return;\n        }\n\n        // Required Fields\n        var fieldObject = {\n          hitType: \"event\",\n          eventCategory: (0, _format[\"default\"])(category),\n          eventAction: (0, _format[\"default\"])(action)\n        };\n\n        // Optional Fields\n        if (label) {\n          fieldObject.eventLabel = (0, _format[\"default\"])(label);\n        }\n        if (typeof value !== \"undefined\") {\n          if (typeof value !== \"number\") {\n            console.warn(\"Expected `args.value` arg to be a Number.\");\n          } else {\n            fieldObject.eventValue = value;\n          }\n        }\n        if (typeof nonInteraction !== \"undefined\") {\n          if (typeof nonInteraction !== \"boolean\") {\n            console.warn(\"`args.nonInteraction` must be a boolean.\");\n          } else {\n            fieldObject.nonInteraction = nonInteraction;\n          }\n        }\n        if (typeof transport !== \"undefined\") {\n          if (typeof transport !== \"string\") {\n            console.warn(\"`args.transport` must be a string.\");\n          } else {\n            if ([\"beacon\", \"xhr\", \"image\"].indexOf(transport) === -1) {\n              console.warn(\"`args.transport` must be either one of these values: `beacon`, `xhr` or `image`\");\n            }\n            fieldObject.transport = transport;\n          }\n        }\n        _this._gaCommand(\"send\", fieldObject);\n      }\n    });\n    _defineProperty(this, \"send\", function (fieldObject) {\n      _this._gaCommand(\"send\", fieldObject);\n    });\n    this.reset();\n  }\n  _createClass(GA4, [{\n    key: \"gtag\",\n    value: function gtag() {\n      this._gtag.apply(this, arguments);\n    }\n  }]);\n  return GA4;\n}();\nexports.GA4 = GA4;\nvar _default = new GA4();\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-ga4/dist/ga4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-ga4/dist/gtag.js":
/*!*********************************************!*\
  !*** ./node_modules/react-ga4/dist/gtag.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar gtag = function gtag() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  if (typeof window !== \"undefined\") {\n    var _window;\n    if (typeof window.gtag === \"undefined\") {\n      window.dataLayer = window.dataLayer || [];\n      window.gtag = function gtag() {\n        window.dataLayer.push(arguments);\n      };\n    }\n    (_window = window).gtag.apply(_window, args);\n  }\n};\nvar _default = gtag;\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2E0L2Rpc3QvZ3RhZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBa0I7QUFDbEI7QUFDQSxzRUFBc0UsYUFBYTtBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXGNvbnRlbnRpYVxcY29udGVudGlhLWZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LWdhNFxcZGlzdFxcZ3RhZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHNbXCJkZWZhdWx0XCJdID0gdm9pZCAwO1xudmFyIGd0YWcgPSBmdW5jdGlvbiBndGFnKCkge1xuICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICBhcmdzW19rZXldID0gYXJndW1lbnRzW19rZXldO1xuICB9XG4gIGlmICh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIF93aW5kb3c7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cuZ3RhZyA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgd2luZG93LmRhdGFMYXllciA9IHdpbmRvdy5kYXRhTGF5ZXIgfHwgW107XG4gICAgICB3aW5kb3cuZ3RhZyA9IGZ1bmN0aW9uIGd0YWcoKSB7XG4gICAgICAgIHdpbmRvdy5kYXRhTGF5ZXIucHVzaChhcmd1bWVudHMpO1xuICAgICAgfTtcbiAgICB9XG4gICAgKF93aW5kb3cgPSB3aW5kb3cpLmd0YWcuYXBwbHkoX3dpbmRvdywgYXJncyk7XG4gIH1cbn07XG52YXIgX2RlZmF1bHQgPSBndGFnO1xuZXhwb3J0c1tcImRlZmF1bHRcIl0gPSBfZGVmYXVsdDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-ga4/dist/gtag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-ga4/dist/index.js":
/*!**********************************************!*\
  !*** ./node_modules/react-ga4/dist/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = exports.ReactGAImplementation = void 0;\nvar _ga = _interopRequireWildcard(__webpack_require__(/*! ./ga4 */ \"(ssr)/./node_modules/react-ga4/dist/ga4.js\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nvar ReactGAImplementation = _ga.GA4;\nexports.ReactGAImplementation = ReactGAImplementation;\nvar _default = _ga[\"default\"];\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-ga4/dist/index.js\n");

/***/ })

};
;