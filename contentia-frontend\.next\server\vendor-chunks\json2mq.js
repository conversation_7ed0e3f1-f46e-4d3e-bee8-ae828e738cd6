/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json2mq";
exports.ids = ["vendor-chunks/json2mq"];
exports.modules = {

/***/ "(ssr)/./node_modules/json2mq/index.js":
/*!***************************************!*\
  !*** ./node_modules/json2mq/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var camel2hyphen = __webpack_require__(/*! string-convert/camel2hyphen */ \"(ssr)/./node_modules/string-convert/camel2hyphen.js\");\n\nvar isDimension = function (feature) {\n  var re = /[height|width]$/;\n  return re.test(feature);\n};\n\nvar obj2mq = function (obj) {\n  var mq = '';\n  var features = Object.keys(obj);\n  features.forEach(function (feature, index) {\n    var value = obj[feature];\n    feature = camel2hyphen(feature);\n    // Add px to dimension features\n    if (isDimension(feature) && typeof value === 'number') {\n      value = value + 'px';\n    }\n    if (value === true) {\n      mq += feature;\n    } else if (value === false) {\n      mq += 'not ' + feature;\n    } else {\n      mq += '(' + feature + ': ' + value + ')';\n    }\n    if (index < features.length-1) {\n      mq += ' and '\n    }\n  });\n  return mq;\n};\n\nvar json2mq = function (query) {\n  var mq = '';\n  if (typeof query === 'string') {\n    return query;\n  }\n  // Handling array of media queries\n  if (query instanceof Array) {\n    query.forEach(function (q, index) {\n      mq += obj2mq(q);\n      if (index < query.length-1) {\n        mq += ', '\n      }\n    });\n    return mq;\n  }\n  // Handling single media query\n  return obj2mq(query);\n};\n\nmodule.exports = json2mq;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json2mq/index.js\n");

/***/ })

};
;